const API_KEY = "sk-yw12342234";

// 可用模型列表 - 基于示例代码更新
const AVAILABLE_MODELS = {
  "scira-default": "Grok3-mini", // thinking model
  "scira-grok-3": "Grok3",
  "scira-anthropic": "Claude 4 Sonnet",
  "scira-anthropic-thinking": "Claude 4 Sonnet Thinking", // thinking model
  "scira-vision": "Grok2-Vision", // vision model
  "scira-4o": "GPT4o",
  "scira-qwq": "QWQ-32B",
  "scira-o4-mini": "o4-mini",
  "scira-google": "gemini 2.5 flash Thinking", // thinking model
  "scira-google-pro": "gemini 2.5 pro",
  "scira-llama-4": "llama 4 Maverick",
};

// 生成更真实的浏览器指纹
function generateBrowserFingerprint() {
  const browsers = [
    {
      name: "Chrome",
      platforms: ["Windows", "macOS", "Linux"],
      versions: ["120", "121", "122", "123", "124", "125", "126", "127"],
      template: (platform, version, build, patch) =>
        `Mozilla/5.0 (${platform === "Windows" ? "Windows NT 10.0; Win64; x64" :
         platform === "macOS" ? "Macintosh; Intel Mac OS X 10_15_7" :
         "X11; Linux x86_64"}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version}.0.${build}.${patch} Safari/537.36`
    },
    {
      name: "Edge",
      platforms: ["Windows", "macOS"],
      versions: ["120", "121", "122", "123", "124"],
      template: (platform, version, build, patch) =>
        `Mozilla/5.0 (${platform === "Windows" ? "Windows NT 10.0; Win64; x64" :
         "Macintosh; Intel Mac OS X 10_15_7"}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version}.0.0.0 Safari/537.36 Edg/${version}.0.${build}.${patch}`
    }
  ];

  const languages = ["en-US,en;q=0.9", "zh-CN,zh;q=0.9", "en-GB,en;q=0.9", "ja-JP,ja;q=0.8", "fr-FR,fr;q=0.8"];
  const timezones = ["America/New_York", "Europe/London", "Asia/Shanghai", "Asia/Tokyo", "Europe/Paris"];

  const browser = browsers[Math.floor(Math.random() * browsers.length)];
  const platform = browser.platforms[Math.floor(Math.random() * browser.platforms.length)];
  const version = browser.versions[Math.floor(Math.random() * browser.versions.length)];
  const language = languages[Math.floor(Math.random() * languages.length)];
  const timezone = timezones[Math.floor(Math.random() * timezones.length)];

  // 生成更真实的版本号
  const buildVersion = Math.floor(Math.random() * 9999) + 1000;
  const patchVersion = Math.floor(Math.random() * 999) + 100;

  return {
    platform,
    version,
    language,
    timezone,
    browserName: browser.name,
    userAgent: browser.template(platform, version, buildVersion, patchVersion),
    secChUa: browser.name === "Chrome" ?
      `"Not)A;Brand";v="99", "Google Chrome";v="${version}", "Chromium";v="${version}"` :
      `"Not)A;Brand";v="99", "Microsoft Edge";v="${version}", "Chromium";v="${version}"`,
    sessionId: crypto.randomUUID(),
  };
}

// 全局浏览器指纹
let currentFingerprint = generateBrowserFingerprint();

// 简单的请求频率限制
const requestTracker = {
  requests: [],
  maxRequestsPerMinute: 30, // 每分钟最多30个请求

  canMakeRequest() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // 清理一分钟前的请求记录
    this.requests = this.requests.filter(time => time > oneMinuteAgo);

    return this.requests.length < this.maxRequestsPerMinute;
  },

  recordRequest() {
    this.requests.push(Date.now());
  },

  getWaitTime() {
    if (this.requests.length === 0) return 0;
    const oldestRequest = Math.min(...this.requests);
    const waitTime = 60000 - (Date.now() - oldestRequest);
    return Math.max(0, waitTime);
  }
};

function getDefaultHeaders() {
  return {
    "Accept": "text/event-stream,application/json,*/*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": currentFingerprint.language,
    "Content-Type": "application/json",
    "Origin": "https://scira.ai",
    "Referer": "https://scira.ai/",
    "Sec-CH-UA": currentFingerprint.secChUa,
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": `"${currentFingerprint.platform}"`,
    "User-Agent": currentFingerprint.userAgent,
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache",
    "DNT": "1",
    "X-Requested-With": "XMLHttpRequest",
    "Connection": "keep-alive"
  };
}

// Scira 内容提取器 - 基于示例代码
function sciraExtractor(chunk) {
  if (typeof chunk === 'string') {
    // 查找格式为 0:"..." 的内容，可能后跟逗号或字符串结尾
    const match = chunk.match(/0:"(.*?)"(?=,|$)/);
    if (match) {
      // 解码可能的 unicode 转义序列如 \u00e9 并处理转义的引号/反斜杠
      try {
        let content = match[1];
        // 处理 unicode 转义
        content = content.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
          return String.fromCharCode(parseInt(code, 16));
        });
        // 处理转义字符
        content = content.replace(/\\\\/g, '\\').replace(/\\"/g, '"');
        // 处理换行符转义
        content = content.replace(/\\n/g, '\n').replace(/\\r/g, '\r').replace(/\\t/g, '\t');
        return content;
      } catch (e) {
        console.error('Content extraction error:', e);
        return null;
      }
    }
  }
  return null;
}

// 刷新浏览器身份
function refreshIdentity() {
  currentFingerprint = generateBrowserFingerprint();
  console.log('Browser identity refreshed:', {
    platform: currentFingerprint.platform,
    version: currentFingerprint.version,
    sessionId: currentFingerprint.sessionId.substring(0, 8)
  });
}

// 添加延迟函数
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 计算指数退避延迟
function getBackoffDelay(retryCount) {
  const baseDelay = 1000; // 1秒基础延迟
  const maxDelay = 30000; // 最大30秒
  const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);
  // 添加随机抖动，避免雷群效应
  const jitter = Math.random() * 0.3 * delay;
  return delay + jitter;
}

// 检查是否是Vercel安全检查页面
function isVercelSecurityCheckpoint(text) {
  return text.includes('Vercel Security Checkpoint') ||
         text.includes('data-astro-cid') ||
         text.includes('<!DOCTYPE html>') && text.includes('Vercel');
}

// 生成更随机的请求间隔
function getRandomDelay() {
  // 在500ms到2000ms之间随机延迟
  return Math.random() * 1500 + 500;
}

// 处理和合并 system 消息
function processSystemMessages(messages) {
  const systemMessages = messages.filter(msg => msg.role === "system");
  const nonSystemMessages = messages.filter(msg => msg.role !== "system");

  if (systemMessages.length === 0) {
    return messages;
  }

  if (systemMessages.length === 1) {
    return messages;
  }

  // 收集所有 system 消息的内容
  const systemContents = systemMessages
    .map(msg => (msg.content || "").trim())
    .filter(content => content.length > 0);

  // 去重：如果内容完全相同，只保留一个
  const uniqueContents = [...new Set(systemContents)];

  // 如果去重后只有一个内容，使用单个 system 消息
  if (uniqueContents.length <= 1) {
    const mergedContent = uniqueContents[0] || "";
    return [
      { role: "system", content: mergedContent },
      ...nonSystemMessages
    ];
  }

  // 如果有多个不同的 system 内容，合并它们
  const mergedContent = uniqueContents.join('\n\n');

  return [
    { role: "system", content: mergedContent },
    ...nonSystemMessages
  ];
}

async function handleStreamResponse(response) {
  // 设置响应头
  const headers = new Headers({
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Access-Control-Allow-Origin": "*",
  });

  try {
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    const encoder = new TextEncoder();
    const decoder = new TextDecoder("utf-8");

    // 创建响应流
    const stream = response.body;
    let buffer = "";

    // 处理响应流
    const reader = stream.getReader();
    const sentText = async (text) => {
      const obj = `data: ${JSON.stringify({
        id: crypto.randomUUID(),
        object: "chat.completion.chunk",
        created: new Date().getTime(),
        choices: [
          {
            index: 0,
            delta: {
              content: text,
              role: "assistant",
            },
          },
        ],
      })}\n\n`;
      await writer.write(encoder.encode(obj));
    };

    (async () => {
      try {
        let thinking = false;
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            if (thinking) {
              await sentText("</think>");
            }
            await writer.write(encoder.encode("data: [DONE]\n\n"));
            await writer.close();
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (!line.trim()) continue;

            // 处理思考模式（g 类型）
            if (line.startsWith('g:')) {
              if (!thinking) {
                thinking = true;
                await sentText("<think>");
              }
              try {
                const content = line.substring(2);
                const data = JSON.parse(content);
                await sentText(data);
              } catch (e) {
                console.error('G-type parsing error:', e);
              }
              continue;
            }

            // 如果当前在思考模式，但遇到非 g: 开头的行，结束思考模式
            if (thinking && !line.startsWith('g:')) {
              thinking = false;
              await sentText("</think>");
            }

            // 处理普通内容（0: 开头）
            const extractedContent = sciraExtractor(line);
            if (extractedContent) {
              await sentText(extractedContent);
            }
          }
        }
      } catch (error) {
        console.error("流处理错误:", error);
        await sentText(`\n\n[错误: ${error.message}]`);
        await writer.write(encoder.encode("data: [DONE]\n\n"));
        await writer.close();
      }
    })();

    return new Response(readable, { headers });
  } catch (error) {
    console.error("处理响应错误:", error);
    return new Response("data: [DONE]\n\n", { headers });
  }
}

async function handleNonStreamResponse(response) {
  const headers = new Headers({
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
  });

  try {
    const decoder = new TextDecoder("utf-8");
    const stream = response.body;
    const reader = stream.getReader();
    let fullResponse = "";
    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (!line.trim()) continue;

        // 使用改进的提取器
        const extractedContent = sciraExtractor(line);
        if (extractedContent) {
          fullResponse += extractedContent;
        }
      }
    }

    // 处理剩余的 buffer
    if (buffer.trim()) {
      const extractedContent = sciraExtractor(buffer);
      if (extractedContent) {
        fullResponse += extractedContent;
      }
    }

    return new Response(JSON.stringify({
      id: crypto.randomUUID(),
      object: "chat.completion",
      created: new Date().getTime(),
      choices: [{
        index: 0,
        message: {
          role: "assistant",
          content: fullResponse
        },
        finish_reason: "stop"
      }],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      }
    }), { headers });

  } catch (error) {
    console.error("处理响应错误:", error);
    throw error;
  }
}

// 验证 API 密钥
function verifyApiKey(request) {
  const authorization = request.headers.get("Authorization");
  if (!authorization) {
    return new Response(JSON.stringify({ error: "Missing API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  const apiKey = authorization.replace("Bearer ", "").trim();
  if (apiKey !== API_KEY) {
    return new Response(JSON.stringify({ error: "Invalid API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  return null;
}

async function handleRequest(request) {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  const url = new URL(request.url);

  // 验证 API 密钥（除了 OPTIONS 请求）
  const authError = verifyApiKey(request);
  if (authError) return authError;

  if (request.method === "GET" && url.pathname === "/v1/models") {
    // 基于 AVAILABLE_MODELS 生成模型列表
    const modelList = Object.keys(AVAILABLE_MODELS).map(modelId => ({
      id: modelId,
      object: "model",
      created: 1686935002,
      owned_by: "scira",
      description: AVAILABLE_MODELS[modelId]
    }));

    // 添加搜索版本的模型
    const searchModels = Object.keys(AVAILABLE_MODELS)
      .filter(modelId => !modelId.includes('vision')) // 视觉模型不支持搜索
      .map(modelId => ({
        id: `${modelId}-search`,
        object: "model",
        created: 1686935002,
        owned_by: "scira",
        description: `${AVAILABLE_MODELS[modelId]} (with web search)`
      }));

    return new Response(
      JSON.stringify({
        object: "list",
        data: [...modelList, ...searchModels],
      }),
      {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }

  if (request.method === "POST" && url.pathname === "/v1/chat/completions") {
    // 检查请求频率限制
    if (!requestTracker.canMakeRequest()) {
      const waitTime = requestTracker.getWaitTime();
      return new Response(
        JSON.stringify({
          error: {
            message: `Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds before making another request.`,
            type: "rate_limit_error",
            param: null,
            code: "rate_limit_exceeded",
          },
        }),
        {
          status: 429,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
            "Retry-After": Math.ceil(waitTime / 1000).toString(),
          },
        }
      );
    }

    // 记录请求
    requestTracker.recordRequest();

    const body = await request.json();

    try {
      // 验证模型
      let modelName = body.model;
      let group = "chat";

      // 处理搜索模式
      if (modelName.endsWith('-search')) {
        group = "web";
        modelName = modelName.replace('-search', '');
      }

      // 验证模型是否存在
      if (!AVAILABLE_MODELS[modelName]) {
        return new Response(
          JSON.stringify({
            error: {
              message: `Model '${body.model}' not found. Available models: ${Object.keys(AVAILABLE_MODELS).join(', ')}`,
              type: "invalid_request_error",
              param: "model",
              code: "model_not_found",
            },
          }),
          {
            status: 400,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
          }
        );
      }

      // 生成唯一ID
      const chatId = crypto.randomUUID();
      const userId = `user_${crypto.randomUUID().substring(0, 8).toUpperCase()}`;

      // 首先处理和合并 system 消息
      const normalizedMessages = processSystemMessages(body.messages);

      // 处理消息格式 - 基于示例代码
      const processedMessages = normalizedMessages.map(msg => {
        // 处理系统消息
        if (msg.role === "system") {
          return {
            role: "system",
            content: msg.content || ""
          };
        }

        // 处理用户消息
        const processedMsg = {
          role: msg.role,
          content: msg.content || "",
          parts: [{ type: "text", text: msg.content || "" }]
        };

        // 处理附件（新格式）
        if (msg.experimental_attachments && msg.experimental_attachments.length > 0) {
          processedMsg.experimental_attachments = msg.experimental_attachments;
        }

        // 处理旧格式的图片（通过content数组）
        if (Array.isArray(msg.content)) {
          const textContent = msg.content.find(c => c.type === "text")?.text || "";
          const attachments = msg.content
            .filter(c => c.type === "image_url")
            .map(img => ({
              name: new URL(img.image_url.url).pathname.split("/").pop() || "image.jpg",
              contentType: "image/jpeg",
              url: img.image_url.url,
              size: 0
            }));

          processedMsg.content = textContent;
          processedMsg.parts = [{ type: "text", text: textContent }];
          if (attachments.length > 0) {
            processedMsg.experimental_attachments = attachments;
          }
        }

        return processedMsg;
      });

      // 构建请求负载 - 基于示例代码格式
      const transformedBody = {
        id: chatId,
        messages: processedMessages,
        model: modelName,
        group: group,
        user_id: userId,
        timezone: "Asia/Shanghai"
      };

      // 发送请求，包含改进的错误处理和重试机制
      let response;
      let retryCount = 0;
      const maxRetries = 3; // 增加重试次数

      while (retryCount <= maxRetries) {
        try {
          // 如果是重试，先等待指数退避延迟
          if (retryCount > 0) {
            const delayMs = getBackoffDelay(retryCount - 1);
            console.log(`Waiting ${Math.round(delayMs)}ms before retry ${retryCount}...`);
            await delay(delayMs);
          } else {
            // 即使是第一次请求，也添加小的随机延迟
            const randomDelayMs = getRandomDelay();
            await delay(randomDelayMs);
          }

          response = await fetch("https://scira.ai/api/search", {
            method: "POST",
            headers: getDefaultHeaders(),
            body: JSON.stringify(transformedBody),
          });

          // 检查响应状态
          if (response.status === 403 || response.status === 429) {
            if (retryCount < maxRetries) {
              console.log(`Received status ${response.status}, refreshing identity and retrying...`);
              refreshIdentity();
              retryCount++;
              continue;
            } else {
              // 最后一次重试失败，返回更友好的错误信息
              throw new Error(`Rate limited (${response.status}). Please try again later.`);
            }
          }

          if (response.status === 500) {
            const errorText = await response.text();
            // 检查是否是Vercel安全检查页面
            if (isVercelSecurityCheckpoint(errorText)) {
              if (retryCount < maxRetries) {
                console.log('Detected Vercel security checkpoint, refreshing identity and retrying...');
                refreshIdentity();
                retryCount++;
                continue;
              } else {
                throw new Error('Request blocked by security checkpoint. Please try again later.');
              }
            }
            throw new Error(`Server error: ${errorText.substring(0, 200)}...`);
          }

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText.substring(0, 200)}...`);
          }

          break;
        } catch (error) {
          if (retryCount >= maxRetries) {
            throw error;
          }
          retryCount++;
          console.log(`Request failed, retrying (${retryCount}/${maxRetries}):`, error.message);
        }
      }

      // 根据 stream 参数选择处理方式
      return body.stream
        ? await handleStreamResponse(response)
        : await handleNonStreamResponse(response);

    } catch (error) {
      console.error("Request processing error:", error);
      return new Response(
        JSON.stringify({
          error: {
            message: error.message || "Internal server error",
            type: "server_error",
            param: null,
            code: error.code || "internal_error",
          },
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }
  }

  return new Response("Not Found", { status: 404 });
}


// Workers 入口点
export default {
  async fetch(request) {
    return handleRequest(request);
  },
};